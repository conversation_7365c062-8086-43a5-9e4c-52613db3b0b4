<template>
  <v-container
    fluid
    :class="['pa-0', { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }]"
  >
    <!-- Top Banner Start -->
    <v-row no-gutters justify="center" class="pa-4 pa-md-6 px-lg-0 px-xl-0">
      <v-col cols="12" lg="9" class="mw-75-v">
        <banner :banner-adapt="bannerAdapt" />
      </v-col>
    </v-row>
    <!-- Top Banner End -->
    <!-- Games Content Start -->
    <v-lazy>
      <v-row class="text-center justify-center" no-gutters>
        <v-col cols="12" lg="9" class="mb-sm-8 mb-6 px-lg-0 px-sm-3 px-4 mw-75-v">
          <gameIndex id="gameIndex" />
        </v-col>
      </v-row>
    </v-lazy>
  </v-container>
</template>

<script>
  import banner from '@/mixins/banner'
  import orientation from '@/mixins/orientation.js'

  export default {
    name: 'Index',
    mixins: [banner, orientation],
    components: {
      gameIndex: () => import('~/components/game/index.vue'),
      banner: () => import('~/components/banner')
    },
    data() {
      return {
        banner: []
      }
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      bannerSource({ banner }) {
        const source = {}
        if (banner.length) {
          banner.forEach((item) => {
            source[item.source] = {
              event: () => {
                switch (item.displayMethod) {
                  case 'new_window':
                    if (item.link !== null) {
                      this.$lineOpenWindow.open(item.link, '_blank')
                    }
                    break
                  case 'redirect':
                    if (item.link !== null) {
                      const isIncludeOrigin = item.link.includes(location.origin)
                      if (!/http/.test(item.link) || isIncludeOrigin) {
                        //若是網址內沒有http則直接跳轉內部網頁
                        //若是網址內有http且網址內有location.origin則直接跳轉內部網頁
                        const link = item.link.startsWith('/') ? item.link : '/' + item.link
                        let url = new URL(
                          !/http/.test(item.link) ? `${location.origin + link}` : item.link
                        )
                        let urlParams = this.parseUrlParams(url)

                        this.$router.push({
                          path: this.localePath(
                            isIncludeOrigin ? item.link.split(location.origin)[1] : link
                          ),
                          query: { ...urlParams }
                        })
                      } else {
                        window.location.href = item.link
                      }
                    }
                    break
                }
              }
            }
            source[item.source][this.$i18n.locale] = {
              lg: '',
              xs: ''
            }

            item.thumbs.forEach((img) => {
              source[item.source][img.lang][
                img.sizeCode
              ] = `${process.env.IMAGE_URL}${img.thumbPath}`
            })
          })
        }
        return source
      },
      bannerAdapt({ bannerSource, $i18n: { locale }, $vuetify: { breakpoint } }) {
        const src = {}
        const point = breakpoint.mdAndUp ? 'lg' : 'xs'
        for (const key in bannerSource) {
          if (!src[key]) {
            src[key] = {}
          }
          src[key].src = bannerSource[key][locale][point]
          src[key].event = bannerSource[key].event
        }
        return src
      },
      station({ $store }) {
        return $store.getters['station']
      },
      bannerDefault() {
        return this.$vuetify.breakpoint.smAndUp
          ? `${process.env.IMAGE_URL}/banner/${this.station.client_id}/default_banner_lg.webp`
          : `${process.env.IMAGE_URL}/banner/${this.station.client_id}/default_banner_xs.webp`
      }
    },
    created() {
      this.getBanners()
    },
    mounted() {},
    methods: {
      async getBanners() {
        if (this.maintainSystem[0].maintaining) {
          return
        }
        const bannerData = await this.$clientApi.banner.lists({ lang: this.$i18n.locale })
        if (!bannerData.errorCode) {
          this.banner = bannerData.data.list
        } else {
          this.$notify.backendError(bannerData.errorCode)
        }
      }
    },
    watch: {
      'maintainSystem.0.maintaining': {
        handler(val) {
          // 平台維護檢查
          if (val === false) {
            this.getBanners()
          }
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .border-line {
    &::after {
      width: 99.7%;
      height: 99.4%;
    }
  }

  @media screen and (max-width: 600px) {
    .border-line {
      &::after {
        width: 99.5%;
        height: 99.7%;
      }
    }
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
