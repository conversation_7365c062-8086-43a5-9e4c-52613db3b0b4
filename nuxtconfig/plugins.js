const STATION = process.env.STATION
const plugins = [
  { src: '@/plugins/validate', ssr: true },
  { src: '@/plugins/moment', ssr: true },
  // { src: '@/plugins/lodash', ssr: true },
  // { src: '@/plugins/backend/api', ssr: true },
  { src: '@/plugins/backend/clientApi', ssr: true },
  { src: '@/plugins/backend/xinUserPhoto', ssr: true },
  { src: '@/plugins/backend/xinSemantic', ssr: true },
  { src: '@/plugins/backend/xinUserProfile', ssr: true },
  { src: '@/plugins/backend/xinStorage', ssr: true },
  { src: '@/plugins/backend/qppApi', ssr: true },
  { src: '@/plugins/backend/gashApi', ssr: true },
  { src: '@/plugins/error_handler', ssr: true },
  { src: '@/plugins/phone_number_validator', ssr: true },
  { src: '@/plugins/filter', ssr: true },
  { src: '@/plugins/timer', ssr: true },
  { src: '@/plugins/count_animate', ssr: true },
  { src: '@/plugins/notifications', ssr: false },
  { src: '@/plugins/swiper', ssr: false },
  // { src: '@/plugins/charts', ssr: false },
  { src: '@/plugins/clipboard', ssr: false },
  { src: '@/plugins/xin-socket/wsClient', ssr: false },
  { src: '@/plugins/qrcode', ssr: false },
  { src: '@/plugins/vue_cropper', ssr: false },
  { src: '@/plugins/vue_advanced_cropper.js', ssr: false },
  { src: '@/plugins/html2canvas', ssr: false },
  // { src: '@/plugins/quill', ssr: false },
  // { src: '@/plugins/video', ssr: false },
  { src: '@/plugins/device', ssr: false },
  { src: '@/plugins/facebook-sdk.js', ssr: false },
  { src: '@/plugins/gapi-sdk.js', ssr: false },
  { src: '@/plugins/apple-sdk.js', ssr: false },
  { src: '@/plugins/screenfull.js', ssr: false },
  { src: '@/plugins/gtag.js', ssr: false },
  { src: '@/plugins/exif.js', ssr: false },
  // { src: '~/plugins/vue_lazyload', ssr: false },
  // { src: '@/plugins/mark', ssr: false },
  // { src: '@/plugins/line_track', ssr: false }
  // { src: '@/plugins/nuxt-compress', ssr: false },
  // { src: '@/plugins/iframe-resize', ssr: false },
  '@/plugins/local_storage',
  '@/plugins/string-format',
  `@/station/${STATION}/ui.config`,
  // { src: '~/plugins/nosleep.js', ssr: false },
  { src: '~/plugins/page_lifcycle.js', ssr: false },
  { src: '~/plugins/gtm.js', ssr: false },
  { src: '~/plugins/liff.js', ssr: false },
  { src: '~/plugins/windowOpen.js', ssr: false },
  { src: '~/plugins/ios_splash.js', ssr: false },
  { src: '~/plugins/lottie.js', ssr: false }
]
if (STATION === 'taiwan_02') {
  plugins.push({ src: '~/plugins/backend/waninNorth', ssr: false })
}
export default {
  // Plugins to run before rendering page (https://go.nuxtjs.dev/config-plugins)
  plugins
}
