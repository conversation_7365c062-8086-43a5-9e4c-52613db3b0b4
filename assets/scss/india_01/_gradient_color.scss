$pv1-color: map-get($colors, primary-variant-1);
$pv2-color: map-get($colors, primary-variant-2);
$pv3-color: map-get($colors, primary-variant-3);
$grey-1-color: map-get($colors, grey-1);
$grey-2-color: map-get($colors, grey-2);
$grey-3-color: map-get($colors, grey-3);
$grey-4-color: map-get($colors, grey-4);
$grey-5-color: map-get($colors, grey-5);
$primary-color: map-get($colors, primary);

.gradient-primary {
  background: linear-gradient(180deg, #fcf3dd 0%, #dfc181 100%);
}

.gradient-primary-reverse {
  background: linear-gradient(180deg, #dfc181 0%, #fcf3dd 100%);
}

.gradient-primary-left {
  background: linear-gradient(270deg, #857555 0%, #dfc181 100%);
}

.gradient-light-primary {
  background: linear-gradient(180deg, rgba(102, 191, 255, 0.8) 0%, rgba(65, 118, 250, 1) 100%);
}

.gradient-white-card {
  background: linear-gradient(180deg, $white-color 29.17%, $grey-4-color 92.71%, $grey-5-color 100%);
}

.gradient-button {
  background: linear-gradient(180deg, #ffedd4 0%, #dfc181 100%);
}

.gradient-game-maintenance {
  background: linear-gradient(180deg, rgba(181, 186, 197, 0.5) 0%, #222730 100%);
}

.gradient-primary-box {
  background: linear-gradient(270.47deg, #cbd8ec -0.45%, #253755 99.59%);
}

.gradient-title--text {
  background: linear-gradient(180deg, #ffe6c2 0%, #dfc181 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}
.gradient-subtitle--text {
  background: linear-gradient(180deg, $grey-2-color 61.46%, $grey-4-color 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
}

.gradient-primary--text {
  background: linear-gradient(180deg, #fcf3dd 0%, #dfc181 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  border-radius: 4px;
}

.gradient-primary-border {
  border: 3px solid;
  border-image: linear-gradient(180deg, rgba(255, 231, 189, 1), rgba(247, 182, 117, 1)) 1;
}

.app-bar-gradient {
  background: #162e5d !important;
}
.bottom-bar-gradient {
  background: #162e5d !important;
}

.app-bar-button {
  background: linear-gradient(180deg, #fcf3dd 0%, #dfc181 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-line {
  width: 100%;
  height: 100%;
  border: solid 1px transparent;
  background-image: linear-gradient(360deg, #d39f43 0%, #ffffff 50.52%, #d39f43 100%);
  background-origin: border-box;
}

.card-gradient-1 {
  background: linear-gradient(180deg, #ac2335 29.17%, #550d10 92.71%, #a0574c 100%);
}

.card-gradient-2 {
  background: linear-gradient(180deg, #09315e 0%, #091842 100%);
}

.background {
  background: #051f42;
}

.info-card {
  background: linear-gradient(180deg, #3a6392 0%, #051f42 100%);
}

.vip-title {
  background: linear-gradient(180deg, #ff3406 0%, #db9c05 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-game-maintenance-2 {
  background: linear-gradient(180deg, rgba(181, 186, 197, 0) 0%, #222730 100%);
}

.gradient-side-bar-game {
  background: linear-gradient(99.09deg, #083591 -2.56%, #0c65cc 37.71%, #c4cbff 109.3%);
}

.game-intro-grade-bg {
  background: linear-gradient(180deg, #4169a9 0%, rgba(73, 124, 160, 0.4) 50%, rgba(113, 159, 198, 0) 100%) !important;
}

.game-intro-grade-border {
  background: linear-gradient(270deg, #283f65 0%, #3a6392 28%, rgba(69, 114, 161, 0.2) 76%, rgba(18, 40, 84, 0.2) 100%);
  &::before {
    border: 2px solid;
    border-image: linear-gradient(
        137.18deg,
        #bac8d6 -0.44%,
        #4572a1 49.78%,
        rgba(43, 68, 114, 0.2) 74.89%,
        #cbd8ec 87.45%,
        rgba(43, 68, 114, 0.2) 100%
      )
      1;
  }
}
.mail-attachment-gradient-bg {
  background: linear-gradient(180deg, #6573f2 0%, rgba(101, 115, 242, 0.4) 50%, rgba(101, 115, 242, 0) 100%);
}

.gift-pack-reward-card-fill {
  background: linear-gradient(
    270deg,
    rgba(241, 142, 28, 0) 0%,
    rgba(241, 142, 28, 0.5) 12.86%,
    #f18e1c 50%,
    rgba(241, 142, 28, 0.5) 85%,
    rgba(241, 142, 28, 0) 100%
  );
}

.gift-pack-frame {
  background: linear-gradient(
    270deg,
    rgba(113, 159, 198, 0) 0%,
    rgba(113, 159, 198, 0.8) 51%,
    rgba(113, 159, 198, 0) 100%
  );
}
.gift-pack-frame-vip {
  background: linear-gradient(270deg, rgba(50, 89, 138, 0) 0%, rgba(50, 89, 138, 0.8) 51%, rgba(50, 89, 138, 0) 100%);
}
.gradient-table {
  background: linear-gradient(270deg, #000a24 0%, #224273 100%);
}
