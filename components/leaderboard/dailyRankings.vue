<template>
  <v-container v-if="this.$UIConfig.lock.leaderboard" class="pa-0" fluid style="min-height: 384px">
    <v-row class="ma-0">
      <v-col class="pa-0" cols="12">
        <v-card
          class="ranking pa-0 rounded-tr-0 rounded-bl-0 text-right"
          :class="!isBreakpoint.homeRankingData.sm && !$vuetify.breakpoint.xl ? 'my-5' : 'my-8'"
          elevation="0"
        >
          <rank-tips-dialog content-type="home">
            <template #hintButton>
              <p class="d-flex align-center ma-0" style="gap: 8px">
                <v-btn x-small icon>
                  <span class="material-symbols-outlined"> info </span>
                </v-btn>
                <span :class="{ 'd-none': isBreakpoint.homeRankingData.xs }">{{
                  $t('about_leaderboard')
                }}</span>
              </p>
            </template>
          </rank-tips-dialog>
          <div class="ranking-content d-flex justify-space-between">
            <div class="ranking-select player-ranking-bn-bg-fill-1 text-center">
              <div class="ranking-select-inner">
                <div class="title-container w-100">
                  <div class="gradient-line left"></div>
                  <h4
                    class="gradient-primary--text text-h4 d-inline-block font-weight-bold custom-text-noto"
                  >
                    {{ $t('leaderboard_dragontiger') }}
                  </h4>
                  <div class="gradient-line right"></div>
                </div>
                <p class="subtitle player-ranking-winners-fill--text ma-0">WINNERS</p>
                <div class="btn-group d-flex flex-column my-8">
                  <v-btn
                    v-for="(tab, index) in rankTabs"
                    :key="tab"
                    text
                    plain
                    :disabled="maintainSystem[0].maintaining || !topPlayersData?.count"
                    class="player-ranking-button-fill"
                    :class="[
                      'w-100',
                      { 'active-btn': !maintainSystem[0].maintaining && activeRank === index }
                    ]"
                    @click="changeRank(index)"
                  >
                    {{ $t(tab) }}
                  </v-btn>
                </div>
                <button
                  v-if="!isBreakpoint.homeRankingData.sm"
                  :disabled="maintainSystem[0].maintaining || !topPlayersData?.count"
                  class="capsule-btn w-100"
                  :class="{
                    'disabled-btn': maintainSystem[0].maintaining || !topPlayersData?.count
                  }"
                  @click="$router.push({ path: localePath('/leaderboard') })"
                >
                  <span class="font-weight-medium">{{ $t('explore_all') }}</span>
                </button>
              </div>
            </div>
            <div
              :class="[
                'ranking-winners',
                {
                  'd-block': isBreakpoint.homeRankingData.sm,
                  'data-empty': maintainSystem[0].maintaining || !topPlayersData?.count
                }
              ]"
            >
              <v-fade-transition mode="out-in">
                <rank-flags
                  @open-player-info="handleOpenPlayerInfo"
                  @open-platform-game="handleOpenPlatformGame"
                  :key="activeRank"
                ></rank-flags>
              </v-fade-transition>
              <button
                v-if="isBreakpoint.homeRankingData.sm"
                :disabled="maintainSystem[0].maintaining || !topPlayersData?.count"
                :class="[
                  'capsule-btn w-100',
                  { 'disabled-btn': maintainSystem[0].maintaining || !topPlayersData?.count }
                ]"
                @click="$router.push({ path: localePath('/leaderboard') })"
              >
                <span class="font-weight-medium">{{ $t('explore_all') }}</span>
              </button>
            </div>
            <v-menu
              v-model="playerInfoVisible"
              :position-x="menuX"
              :position-y="menuY"
              absolute
              offset-y
            >
              <easyPlayerInfo
                report
                is-card
                :player-info.sync="selectPlayerData"
                class="elevation-4"
                tile
                only-coin
                badge-type="relation"
                action-bar
              />
            </v-menu>
            <gameIntro
              v-if="showGameIntroDialogStatus"
              :show-game-intro-dialog-status.sync="showGameIntroDialogStatus"
              :game="currGameRtp"
            />
            <app-redirect-dialog
              :show-redirect-app-dialog.sync="showRedirectAppDialog"
            ></app-redirect-dialog>
          </div>
        </v-card>
      </v-col> </v-row
  ></v-container>
</template>

<script>
  import _ from 'lodash'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import relationship from '~/mixins/relationship.js'
  import analytics from '@/mixins/analytics.js'
  import leaderboard from '~/mixins/leaderboard'
  const STATION = process.env.STATION

  export default {
    name: 'DailyRankings',
    components: {
      rankFlags: () => import('~/components/leaderboard/rankFlags.vue'),
      rankTipsDialog: () => import('~/components/leaderboard/rankTipsDialog.vue'),
      easyPlayerInfo: () => import('~/components/player_info/easyPlayerInfo'),
      gameIntro: () => import(`~/components_station/${STATION}/game/gameIntro.vue`),
      appRedirectDialog: () => import('~/components/leaderboard/appRedirectDialog.vue')
    },
    mixins: [preLoginAction, relationship, analytics, leaderboard],
    data() {
      return {
        rankTabs: ['leaderboard_daily_dragon', 'leaderboard_daily_tiger'],
        showRedirectAppDialog: false,
        playerInfoVisible: false,
        currGameRtp: {},
        menuX: 0,
        menuY: 0
      }
    },
    computed: {
      showGameIntroDialogStatus: {
        get() {
          return this.$store.getters['leaderboard/showGameIntroDialogStatus']
        },
        set(value) {
          this.$store.commit('leaderboard/SET_SHOWGAMEINTRODIALOGSTATUS', value)
        }
      },
      dateParams() {
        return {
          type: 0,
          beginAt: this.isPastToday1230
            ? this.dayTimestamp.yesterday
            : this.dayTimestamp.dayBeforeYesterday,
          endAt: this.isPastToday1230
            ? this.dayTimestamp.yesterday
            : this.dayTimestamp.dayBeforeYesterday,
          limit: 3
        }
      }
    },
    async created() {
      this.debouncedHandleRankChange = _.debounce(this.handleRankChange, 300)
      await this.handleRankChange()
    },
    async mounted() {},
    destroyed() {
      this.$store.commit('leaderboard/SET_ACTIVE_RANK', 0)
      this.$store.commit('leaderboard/SET_SHOWGAMEINTRODIALOGSTATUS', false)
    },
    methods: {
      changeRank(index) {
        if (this.activeRank !== index) {
          this.$store.commit('leaderboard/SET_ACTIVE_RANK', index)
          this.debouncedHandleRankChange()
        }
      },
      async handleRankChange() {
        await this.getTopPlayersData(this.dateParams)
        // 取得排行榜資料後，再取得遊戲列表、RTP數據
        if (!this.topPlayersData?.count) return
        await this.getWebGameList()
        await this.fetchGameRtpHandler()
      },
      async handleOpenPlayerInfo(event, username) {
        // TODO:
        this.playerInfoVisible = false
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        if (!this.isLogin) {
          this.setPreLoginAction('playerInfo', this.handleOpenPlayerInfo, event, username)
          this.$nuxt.$emit('root:showLoginDialogStatus', { show: true })
          return
        }
        this.getPlayerInfo(username)
        if (username === this.userName) return

        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            const rect = event.target.getBoundingClientRect()
            // 設置選單位置
            this.menuX = rect.left
            this.menuY = rect.top
            this.playerInfoVisible = true
          })
        })
      },
      async handleOpenPlatformGame(playerRankData) {
        // 取得點擊的遊戲列表
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        if (!playerRankData?.platform) return (this.showRedirectAppDialog = true)
        if (!this.webGameList.length) return this.$notify.warning(this.$t('game_not_available'))
        // 黑名單或遊戲停用則notify警告
        const game = this.webGameList.find(({ id }) => id === playerRankData.gameId)
        if (!game) return this.$notify.warning(this.$t('game_not_available'))
        if (game.blocked) return this.$notify.warning(this.$t('game_access_denied_message'))
        if (!game.hasActive || game.maintaining)
          return this.$notify.warning(this.$t('game_not_available'))
        this.currGameRtp = null
        this.currGameRtp = this.platformChecker(playerRankData)
        this.$store.commit('leaderboard/SET_SHOWGAMEINTRODIALOGSTATUS', true)
      }
    }
  }
</script>

<style lang="scss" scoped>
  $primary: map-get($colors, 'primary');
  $gradient-primary: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%);
  $button-border-color: map-get($colors, 'player-ranking-button-border');
  $button-content: map-get($colors, 'button-content');
  $button-fill-selected: linear-gradient(
    90deg,
    rgba(210, 151, 109, 0) 0%,
    rgba(210, 151, 109, 0.3) 25%,
    rgba(210, 151, 109, 0.8) 50%,
    rgba(210, 151, 109, 0.3) 75%,
    rgba(210, 151, 109, 0) 100%
  );

  .ranking {
    position: relative;
    max-width: 100%;
    background-image: url('@/assets/image/leaderboard/player-ranking-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 12px 0;
    overflow: hidden;

    @supports (background-image: url('.webp')) {
      background-image: url('@/assets/image/leaderboard/player-ranking-bg.webp');
    }

    ::v-deep .ranking-hint {
      display: inline-block;
      position: absolute;
      top: 8px;
      right: 0;
      font-size: 12px;
      letter-spacing: 0.4px;
      z-index: 1;
      cursor: pointer;
      .material-symbols-outlined {
        font-size: 20px !important;
      }

      &:hover {
        p {
          button,
          span {
            color: $primary;
          }
        }
      }
    }

    ::v-deep .ranking-hint-modal {
      max-width: 380px;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%);
      border-radius: 8px;
      width: 95%;
      .v-list-item__subtitle {
        white-space: break-spaces !important;
      }
    }

    &-content {
      min-height: 384px;
      .ranking-select {
        padding-left: 2.1%;
        padding-right: 4.2%;
        width: 35%;
        align-content: center;

        &-inner {
          .title-container {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          h4 {
            white-space: nowrap;
            padding: 0 8px;
          }

          .gradient-line {
            height: 1px;
            flex: 1;
            max-width: 30%;

            &.left {
              background: linear-gradient(270deg, #f7b675 0%, rgba(247, 182, 117, 0) 100%);
            }

            &.right {
              background: linear-gradient(270deg, rgba(247, 182, 117, 0) 0%, #f7b675 100%);
            }
          }

          .subtitle {
            font-size: 20px;
            line-height: 24px;
            letter-spacing: 8px;
          }
        }
      }
      .ranking-winners {
        position: relative;
        padding: 0 2.4%;
        width: 65%;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        justify-content: flex-end;
        place-items: center;
      }

      .data-empty {
        aspect-ratio: 16 / 9;
      }

      .btn-group {
        .v-btn {
          border-radius: 0;
          color: rgba(255, 255, 255, 0.5);
          &:first-child {
            margin-bottom: 12px;
          }
          &:hover {
            color: #fff;
          }
        }

        .active-btn {
          background: $button-fill-selected;
          color: #fff;
          border-bottom: 1px solid $button-border-color;
          ::v-deep .v-btn__content {
            opacity: 1 !important;
          }
        }
      }

      .capsule-btn {
        position: relative;
        padding: 6px 0;
        &:hover {
          background: $gradient-primary;
          border-radius: 28px;

          span {
            color: $button-content;
          }
        }

        span {
          font-size: 14px;
          letter-spacing: 0.5px;
          color: transparent;
          background-image: $gradient-primary;
          background-clip: text;
          -webkit-background-clip: text;
        }

        &::before {
          content: '';
          position: absolute;
          inset: 0;
          border-radius: 28px;
          padding: 1px;
          background: $gradient-primary;
          -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
          -webkit-mask-composite: xor;
          mask-composite: exclude;
        }
      }

      .disabled-btn {
        &:hover {
          background: transparent;
          span {
            color: rgba(255, 255, 255, 0.3);
          }
        }
        span {
          color: rgba(255, 255, 255, 0.3);
          background: unset;
        }
        &::before {
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  @media screen and (max-width: 840px) {
    .ranking {
      &-content {
        flex-direction: column;
        min-height: auto;
        .player-ranking-bn-bg-fill-1 {
          background: linear-gradient(180deg, #7e1635 51.8%, rgba(126, 22, 53, 0) 86.67%);
        }

        .ranking-select {
          width: 100%;
          padding: 36px 2.4% 0;
        }

        .ranking-winners {
          width: 100%;
          place-items: unset;
          .capsule-btn {
            margin: 24px 0;
          }
        }

        .btn-group {
          flex-direction: row !important;
          margin: 24px 0 !important;
          .v-btn {
            width: 50%;
            &:first-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: 599px) {
    .ranking {
      &-content {
        .ranking-select {
          padding-left: 1.6%;
          padding-right: 1.6%;
        }
        .ranking-winners {
          padding: 0 1.6%;
          .capsule-btn {
            margin: 12px 0;
          }
        }
        .btn-group {
          margin: 24px 0 12px !important;
        }
      }
    }
  }
</style>
