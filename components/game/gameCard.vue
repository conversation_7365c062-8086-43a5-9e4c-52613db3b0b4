<template>
  <v-hover v-slot="{ hover: outerHover }">
    <v-card color="transparent" elevation="0" :class="vCardMarginX" ref="gameCard">
      <v-hover>
        <template v-slot:default="{ hover }">
          <v-img
            :lazy-src="gameDefaultJpg"
            :src="gameThumbUrl"
            aspect-ratio="1"
            :placeholder="gameDefaultJpg"
            @error="setAltImg()"
            style="
              border-top-left-radius: 12px;
              border-top-right-radius: 12px;
              border-bottom-right-radius: 12px;
            "
          >
            <div v-if="localGame.enable === 2" class="beta-show">
              <v-card
                class="d-flex justify-center align-center"
                color="gradient-primary-reverse"
                width="60px"
                height="32px"
              >
                <span class="default-content--text text-sm-body-2"> BETA </span>
              </v-card>
            </div>

            <div
              v-if="localGame.maintaining || localGame.platformMaintaining"
              class="d-flex v-card--reveal gradient-game-maintenance h-100-percent"
            >
              <span class="material-symbols-outlined default-content--text" style="font-size: 48px">
                construction
              </span>
            </div>
            <div class="d-flex justify-center align-end h-100-percent pb-2 px-2">
              <span class="default-content--text trade-text-type text-no-wrap">
                {{ brandNameDisplay }}
              </span>
            </div>
            <v-fade-transition>
              <v-overlay
                v-if="hover && !localGame.maintaining"
                absolute
                opacity="0.8"
                color="game-hover"
              >
                <v-container>
                  <template>
                    <v-row no-gutters>
                      <v-col v-if="isLogin">
                        <!-- startPlay -->
                        <v-tooltip top v-model="showVipLevelLimitText" :open-on-hover="false">
                          <template v-slot:activator="{ on }">
                            <v-btn
                              v-if="isVipLevelLimit"
                              class="button-content--text"
                              :small="$vuetify.breakpoint.xsOnly"
                              :color="$UIConfig.defaultBtnColor"
                              depressed
                              :width="$vuetify.breakpoint.xsOnly ? '100%' : '96px'"
                              disabled
                              v-on="isVipLevelLimit ? on : {}"
                            >
                              <v-icon left> mdi-lock-outline </v-icon>
                              {{ $t('startsPlay') }}
                            </v-btn>
                          </template>
                          <span>{{ vipLevelLimitText }}</span>
                        </v-tooltip>
                        <v-btn
                          v-if="!isVipLevelLimit"
                          class="button-content--text"
                          :small="$vuetify.breakpoint.xsOnly"
                          :color="$UIConfig.defaultBtnColor"
                          depressed
                          :width="$vuetify.breakpoint.xsOnly ? '100%' : '96px'"
                          @click="startGameWithClick('play')"
                          :disabled="disabledStatus"
                        >
                          {{ $t('startsPlay') }}
                        </v-btn>
                      </v-col>
                      <v-col v-else-if="!hasDemo">
                        <v-btn
                          class="button-content--text"
                          :small="$vuetify.breakpoint.xsOnly"
                          :color="$UIConfig.defaultBtnColor"
                          depressed
                          :width="$vuetify.breakpoint.xsOnly ? '100%' : '96px'"
                          @click="loginClickHandler"
                          :disabled="disabledStatus"
                        >
                          {{ $t('startsPlay') }}
                        </v-btn>
                      </v-col>
                    </v-row>
                    <!-- freePlay -->
                    <v-row v-if="hasDemo" no-gutters class="pt-2">
                      <v-col>
                        <v-btn
                          class="default-content--text"
                          :small="$vuetify.breakpoint.xsOnly"
                          outlined
                          color="white"
                          :width="$vuetify.breakpoint.xsOnly ? '100%' : '96px'"
                          @click="startGameWithClick('demo')"
                          :disabled="disabledStatus"
                        >
                          {{ $t('freePlay') }}
                        </v-btn>
                      </v-col>
                    </v-row>
                  </template>
                </v-container>
              </v-overlay>
            </v-fade-transition>
          </v-img>
        </template>
      </v-hover>
      <v-card
        color="transparent"
        elevation="0"
        class="default-content--text custom-text-noto text-body-2"
      >
        <v-container fluid class="px-0 pt-2 pb-4">
          <div class="d-flex justify-space-between">
            <!-- 遊戲名稱 -->
            <div class="pr-2">
              <span class="game-name-type game-name-layout text-start">
                {{ localGame.name }}
              </span>
            </div>
            <template>
              <span
                :class="['cursor-pointer', 'material-symbols-outlined']"
                class="grey-3--text material-icons md-20"
                @click="openGameIntroDialog()"
              >
                info
              </span>
            </template>
          </div>
          <v-expand-transition>
            <v-row no-gutters class="pt-2 align-center">
              <div
                v-if="!localGame.maintaining && showMainDailyRtp && showRTPStyle.dailyShowStatus"
                class="rtpShow"
              >
                <rtpShow
                  :background-color="showRTPStyle.dailyBackgroundColor"
                  :rtp="localGame.dailyRtp"
                  :icon="showRTPStyle.dailyIcon"
                  icon-color="white--text"
                  chip-text-color="white"
                  :is-outer-hover="outerHover"
                />
              </div>
            </v-row>
          </v-expand-transition>
        </v-container>
      </v-card>
      <notyNotRealMember
        v-if="showNotyNotRealMemberDialogStatus"
        :show-noty-not-real-member-dialog-status.sync="showNotyNotRealMemberDialogStatus"
      />
      <bothRobotExpNoty
        v-if="showNotyBothRobotExpNotyDialogStatus.show"
        :show-noty-both-robot-exp-noty-dialog-status.sync="showNotyBothRobotExpNotyDialogStatus"
      />
      <noExpGainNoty
        v-else-if="showNotyNoExpGainNotyDialogStatus.show"
        :show-noty-no-exp-gain-noty-dialog-status.sync="showNotyNoExpGainNotyDialogStatus"
      />
      <hasRobotNoty
        v-else-if="showNotyHasRobotNotyDialogStatus.show"
        :show-noty-has-robot-noty-dialog-status.sync="showNotyHasRobotNotyDialogStatus"
      />
      <gameIntro
        v-if="showGameIntroDialogStatus"
        :show-game-intro-dialog-status.sync="showGameIntroDialogStatus"
        :game="localGame"
      />
    </v-card>
  </v-hover>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import utilsGame from '@/utils/game.js'
  const STATION = process.env.STATION
  const playGame = require(`~/mixins_station/${STATION}/playGame`).default
  export default {
    name: 'gameCard',
    mixins: [analytics, preLoginAction, playGame],
    components: {
      rtpShow: () => import(`~/components_station/${STATION}/rtp/rtpShow`),
      notyNotRealMember: () => import('~/components/notifications/notyNotRealMember.vue'),
      bothRobotExpNoty: () => import('~/components/notifications/bothRobotExpNoty.vue'),
      noExpGainNoty: () => import('~/components/notifications/noExpGainNoty.vue'),
      hasRobotNoty: () => import('~/components/notifications/hasRobotNoty.vue'),
      gameIntro: () => import(`~/components_station/${STATION}/game/gameIntro.vue`)
    },
    props: {
      game: {
        type: Object,
        default: () => {}
      },
      vCardMarginX: {
        type: String,
        default: 'mx-2'
      }
    },
    data() {
      return {
        gameDefault: this.$store.getters['gameHall/gameDefaultImg'],
        disabledStatus: false,
        showNotyNotRealMemberDialogStatus: false,
        gameThumbUrl: '',
        gameDefaultJpg: this.$store.getters['gameHall/gameDefaultImgJpg'],
        showGameIntroDialogStatus: false,
        localGame: this.game
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      openGameLock({ $store }) {
        return $store.getters['gameHall/openGameLock']
      },
      vipLevelTitle({ $store }) {
        return $store.getters['role/vipLevelTitle']
      },
      brandName({ $store }) {
        const providers = $store.getters['gameProvider/providers']
        const providerBrandName = providers.find((item) => {
          return item.id === this.localGame.platformId
        })
        return providerBrandName.brand
      },
      // "青銅"玩家特別限制，game.vipLevel為1時限制LV10(含)以上才可遊玩，為99表示LV1即可遊玩
      isVipLevelLimit() {
        return (
          this.vipLevel !== 0 &&
          ((this.localGame.vipLevel === 1 && this.level < 10) ||
            (this.localGame.vipLevel !== 99 && this.vipLevel < this.localGame.vipLevel))
        )
      },
      hasRtp() {
        return this.localGame.rtp !== -1
      },
      hasDemo() {
        return this.localGame.hasDemo !== 0
      },
      hasRobot() {
        return this.localGame.hasRobot !== 0
      },
      hasExp() {
        return this.localGame.hasExp !== 0
      },
      showMainDailyRtp() {
        return this.localGame.rtp !== -1
      },
      defaultRtp() {
        return this.localGame.rtp
      },
      showRTPStyle() {
        return utilsGame.showRTPStyle({
          defaultRtp: this.defaultRtp,
          dailyRtp: this.localGame.dailyRtp,
          weeklyRtp: this.localGame.weeklyRtp,
          monthlyRtp: this.localGame.monthlyRtp,
          config: this.$UIConfig
        })
      },
      // 優化長條件判斷,增加可讀性
      gameVipLevelText() {
        return this.localGame.vipLevel === 1 && this.level < 10
          ? 'LV10'
          : this.$t(this.vipLevelTitle[this.localGame.vipLevel])
      },
      // 抽離重複使用的文字
      brandNameDisplay() {
        return this.brandName.toUpperCase()
      },
      vipLevelLimitText() {
        if (this.gameVipLevelText === 'LV10') {
          return this.$t('game_level_limit_reached', { vipLevelText: this.gameVipLevelText })
        } else {
          return this.$t('game_rank_limit_reached', { vipLevelText: this.gameVipLevelText })
        }
      },
      showVipLevelLimitText() {
        return this.isVipLevelLimit && this.isLogin
      }
    },
    watch: {
      openGameLock: {
        handler(val) {
          this.disabledStatus = val
        }
      },
      game: {
        handler(val) {
          this.localGame = val
          this.gameThumbUrl = val.thumbUrl
        },
        deep: true,
        immediate: true
      }
    },
    mounted() {},
    methods: {
      async setAltImg() {
        console.log('setAltImg', this.gameThumbUrl)
        if (this.gameThumbUrl.includes('default')) {
          return
        }

        this.gameThumbUrl = this.gameThumbUrl.replace('.webp', '.jpg')

        try {
          const imageResponse = await fetch(this.gameThumbUrl)
          if (!imageResponse.ok) {
            this.gameThumbUrl = this.gameDefaultJpg
          }
        } catch (error) {
          this.gameThumbUrl = this.gameDefaultJpg
        }
      },
      async loginClickHandler() {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        this.startGameWithClick('play')
        this.setPreLoginAction('playGame', () => this.startGameWithClick('play'))
      },
      openGameIntroDialog() {
        this.showGameIntroDialogStatus = true
      }
    }
  }
</script>

<style lang="scss" scoped>
  .beta-show {
    position: absolute;
    top: 0px;
    right: 0px;
    z-index: 4;
  }

  .vipLimit-show {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 4;
  }

  .vipLimit-chip {
    padding-top: 2px;
    padding-bottom: 2px;
  }

  .tag-icon {
    font-size: 14px;
    margin-right: 2px;
  }

  .rtpShow {
    width: 92px;
  }

  .no-exp-notice {
    background: rgba(97, 97, 97, 0.9);
  }

  .game-level-show {
    max-width: 90%;
  }
  .game-level-text {
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .trade-text-type {
    text-align: center;

    /* Customize/caption */
    font-family: 'Montserrat';
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-transform: uppercase;
  }

  .game-name-layout {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    align-self: stretch;
  }

  .game-name-type {
    overflow: hidden;
    text-overflow: ellipsis;

    /* Body/body-2 */
    font-family: 'Noto Sans TC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
  }
</style>
