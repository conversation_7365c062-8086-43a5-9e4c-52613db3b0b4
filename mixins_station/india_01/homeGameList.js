import gameRelate from '@/mixins/gameRelate.js'
import utilWhiteList from '@/utils/whiteList.js'
import cloneDeep from 'lodash/cloneDeep'
const NUXT_ENV = process.env.NUXT_ENV
const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

export default {
  mixins: [gameRelate],
  data() {
    return {
      list: {
        inCompleteGame: [],
        recentGame: [],
        liveGame: [],
        hotGame: [],
        fishingGame: [],
        featuredGame: [],
        chessAndCardGame: [],
        newestGame: []
      },
      liveGameSet: {
        max: 15,
        sortType: 2,
        category: 200
      },
      popularGameSet: {
        hotGame: {
          max: 15,
          sortType: 2,
          category: 100
        },
        fishingGame: {
          max: 15,
          sortType: 2,
          category: 600
        },
        chessAndCardGame: {
          max: 15,
          sortType: 2,
          category: 300
        }
      },
      newestGameSet: {
        max: 15,
        sortType: 3
      },
      recentGameSet: {
        max: 10
      }
    }
  },
  computed: {
    providers({ $store }) {
      return $store.getters['gameProvider/providers']
    },
    platformMaintain({ $store }) {
      return $store.getters['maintain/platform']
    },
    allGameList({ $store }) {
      return $store.getters['gameHall/allGameList']
    },
    playedGameList({ $store }) {
      return $store.getters['gameHall/playedGameList']
    },
    isLogin({ $store }) {
      return $store.getters['role/isLogin']
    },
    userName({ $store }) {
      return $store.getters['role/userName']
    },
    inCompleteGameList({ list }) {
      return list.inCompleteGame.length > 0
        ? this.filterList(this.getGameListId(list.inCompleteGame), list.inCompleteGame)
        : []
    },
    recentGameList({ list }) {
      return list.recentGame.length > 0
        ? this.filterList(this.getGameListId(list.recentGame), list.recentGame)
        : []
    },
    liveGameList({ list }) {
      return list.liveGame.length > 0
        ? this.filterList(this.getGameListId(list.liveGame), list.liveGame)
        : []
    },
    hotGameList({ list }) {
      return list.hotGame.length > 0
        ? this.filterList(this.getGameListId(list.hotGame), list.hotGame)
        : []
    },
    fishingGameList({ list }) {
      return list.fishingGame.length > 0
        ? this.filterList(this.getGameListId(list.fishingGame), list.fishingGame)
        : []
    },
    chessAndCardGameList({ list }) {
      return list.chessAndCardGame.length > 0
        ? this.filterList(this.getGameListId(list.chessAndCardGame), list.chessAndCardGame)
        : []
    },
    newestGameList({ list }) {
      return list.newestGame.length > 0
        ? this.filterList(this.getGameListId(list.newestGame), list.newestGame)
        : []
    },
    featuredGameList({ list }) {
      if (list.featuredGame.length > 0) {
        const featuredGames = this.filterFeaturedList(
          this.getGameListId(list.featuredGame),
          list.featuredGame
        )
        const uniqueGroups = new Set(list.featuredGame.map((game) => game.group))
        const uniqueGroupsArray = [...uniqueGroups]
        return {
          group: cloneDeep(uniqueGroupsArray),
          data: cloneDeep(featuredGames)
        }
      } else {
        return {
          group: [],
          data: []
        }
      }
    }
  },
  methods: {
    async init() {
      await this.getMaintainList()
      this.getPopularGameList()
      this.getLiveGameList()
      this.getNewestGameList()
      this.getFeaturedGamesList()
      if (!this.isLogin) {
        this.fetchLocalPlayedGameList()
      } else {
        this.fetchServerPlayedGames()
        // 暫停中遊戲
        // 6/17 通知先關閉該功能
        //this.getInCompleteGameList()
      }
    },
    async getPopularGameList() {
      const getData = async (gameSet) => {
        try {
          const rawGameData = await this.getPopularGamesEvent(
            this.providers,
            gameSet.max,
            gameSet.category,
            gameSet.sortType
          )
          return rawGameData
        } catch (error) {
          return []
        }
      }

      const [hotGames, fishingGames, chessAndCardGames] = await Promise.all([
        getData(this.popularGameSet.hotGame),
        getData(this.popularGameSet.fishingGame),
        getData(this.popularGameSet.chessAndCardGame)
      ])

      this.list.hotGame = cloneDeep(hotGames)
      this.list.fishingGame = cloneDeep(fishingGames)
      this.list.chessAndCardGame = cloneDeep(chessAndCardGames)
    },
    async getLiveGameList() {
      try {
        // 真人遊戲為常駐廣告，不會因為有維護而被篩選掉
        let liveGames = await this.getSortGameListData(
          this.liveGameSet.category,
          this.liveGameSet.sortType
        )
        liveGames = liveGames.count > 0 ? liveGames.list : []
        // 取得遊戲廠商相關資訊
        if (liveGames.length !== 0) {
          this.updateGameMaintainList(liveGames)
          liveGames = liveGames.filter((item) => item.maintaining !== true)
          liveGames.forEach((gameItem) => {
            gameItem.thumbUrl =
              gameItem.thumbPath === null
                ? this.$store.getters['gameHall/gameDefaultImg']
                : `${process.env.IMAGE_URL}${gameItem.thumbPath}`
            gameItem.categoryType = this.liveGameSet.category
          })
        }
        const limitedLiveGames = liveGames.slice(0, this.liveGameSet.max)
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', limitedLiveGames)

        this.list.liveGame = cloneDeep(limitedLiveGames)
      } catch (error) {
        this.list.liveGame = []
      }
    },
    async getNewestGameList() {
      try {
        const rawGameData = await this.getNewestGamesEvent(
          this.providers,
          this.newestGameSet.max,
          this.newestGameSet.sortType
        )

        this.list.newestGame = cloneDeep(rawGameData)
      } catch (error) {
        this.list.newestGame = []
      }
    },
    async getFeaturedGamesList() {
      try {
        const rawGameData = await this.getFeaturedGamesEvent(this.providers)
        this.list.featuredGame = cloneDeep(rawGameData)
      } catch (error) {
        this.list.featuredGame = {
          group: [],
          data: []
        }
      }
    },
    async getRecentGameList() {
      try {
        const rawGameData = await this.getRecentGamesEvent(this.playedGameList)
        this.list.recentGame = cloneDeep(rawGameData)
      } catch (error) {
        this.list.recentGame = []
      }
    },
    async getInCompleteGameList() {
      try {
        const rawGameData = await this.getInCompleteGameData()
        this.list.inCompleteGame = cloneDeep(rawGameData)
      } catch (error) {
        this.list.inCompleteGame = []
      }
    },
    async getPopularGamesData(gameCategoryId, type) {
      let games = await this.getSortGameListData(gameCategoryId, type)
      games = games.count > 0 ? games.list : []

      // 取得遊戲廠商相關資訊
      if (games.length !== 0) {
        // 篩選掉維護的遊戲
        this.updateGameMaintainList(games)
        games = games.filter((item) => item.maintaining !== true)
      }

      return games
    },
    async getPopularGamesEvent(providers, max, gameCategory, sortType) {
      if (providers.length > 0) {
        const hasRtp = (item, provider) =>
          item.platformId == provider.id && provider.hasRtp === true
        const getGames = (showGamesCount, games) => {
          //篩選掉沒有提供rtp的遊戲
          games = games.filter((item) => providers.some((provider) => hasRtp(item, provider)))
          //先呈現 RTP最後更新
          return games.slice(0, showGamesCount)
        }
        let list = []
        // 取得遊戲清單
        await this.getPopularGamesData(gameCategory, sortType).then((games) => {
          games.forEach((item) => {
            item.categoryType = gameCategory
            item.dailyRtp = undefined
            item.weeklyRtp = undefined
            item.monthlyRtp = undefined
            item.thumbUrl =
              item.thumbPath === null
                ? this.$store.getters['gameHall/gameDefaultImg']
                : `${process.env.IMAGE_URL}${item.thumbPath}`
          })
          list = getGames(max, games)
        })

        await this.fetchGameRTPHandler(list)
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', list)
        return list
      }
    },
    async getNewestGamesData(type) {
      let games = await this.getUnCategorizedSortGameListData(type, false)
      games = games.count > 0 ? games.list : []

      // 取得遊戲廠商相關資訊
      if (games.length !== 0) {
        // 篩選掉維護的遊戲
        this.updateGameMaintainList(games)
        games = games.filter((item) => item.maintaining !== true)
      }

      return games
    },
    async getNewestGamesEvent(providers, max, sortType) {
      if (providers.length > 0) {
        const hasRtp = (item, provider) =>
          item.platformId == provider.id && provider.hasRtp === true
        const getGames = (showGamesCount, games) => {
          //篩選掉沒有提供rtp的遊戲
          games = games.filter((item) => providers.some((provider) => hasRtp(item, provider)))
          //先呈現 RTP最後更新
          return games.slice(0, showGamesCount)
        }
        let list = []
        // 取得遊戲清單
        await this.getNewestGamesData(sortType).then((games) => {
          games.forEach((item) => {
            item.categoryType = item.clientCategoryType
            item.dailyRtp = undefined
            item.weeklyRtp = undefined
            item.monthlyRtp = undefined
            item.thumbUrl =
              item.thumbPath === null
                ? this.$store.getters['gameHall/gameDefaultImg']
                : `${process.env.IMAGE_URL}${item.thumbPath}`
          })
          list = getGames(max, games)
        })

        await this.fetchGameRTPHandler(list)
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', list)
        return list
      }
    },
    async getFeaturedGamesDataHandler() {
      let tmp = await this.$axios.get(
        process.env.IMAGE_URL +
          `/index/gameProvider/${loadConfig.client_id}/gameProvider.json?` +
          Math.random()
      )
      const headerData = {
        username: this.$store.getters['role/userName']
          ? this.$store.getters['role/userName']
          : null,
        alias: null
      }
      const gameIds = utilWhiteList.filterRecentGames(
        tmp.data.map((item) => item.id),
        window.location.origin
      )
      const tmpGameList = await this.$clientApi.game.gameList(
        headerData,
        gameIds,
        this.$i18n.locale
      )
      const gameList = tmpGameList.list

      tmp.data.forEach((element) => {
        element.gameImg = process.env.IMAGE_URL + '/index/gameImg/' + element.gameImg

        gameList.forEach((item) => {
          if (element.id === item.id) {
            element.gameCover = `${process.env.IMAGE_URL}${item.thumbPath}`
            element.categoryType = item.categoryType
            element.title = item.name
            element.rtp = item.rtp
            element.enable = item.enable
            element.vipLevel = item.vipLevel
            element.maintainBeginAt = item.maintainBeginAt
            element.maintainEndAt = item.maintainEndAt
            element.maintaining = item.maintaining
            element.platformId = item.platformId
            element.hasExp = item.hasExp
            element.hasRobot = item.hasRobot
            element.hasScrollbar = item.hasScrollbar
            element.dailyRtp = undefined
            element.weeklyRtp = undefined
            element.monthlyRtp = undefined
            element.hasDemo = item.hasDemo
          }
        })
      })
      // 如果 this.$clientApi.game.gameList 沒有配對到遊戲，則不顯示
      const result = tmp.data.filter((item) => item.title !== undefined)
      return result
    },
    async getFeaturedGamesEvent(providers) {
      if (providers.length > 0) {
        // 取得精選遊戲清單
        let getFeaturedGamesData = await this.getFeaturedGamesDataHandler()
        // 先呈現 RTP最後更新
        let featuredGameListTmp = getFeaturedGamesData
        await this.fetchGameRTPHandler(featuredGameListTmp)
        this.updateGameMaintainList(featuredGameListTmp)
        featuredGameListTmp = featuredGameListTmp.filter((item) => item.maintaining !== true)
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', featuredGameListTmp)

        return featuredGameListTmp
      }
    },
    async getRecentGamesEvent(playedGameList) {
      if (playedGameList.length > 0) {
        const gameIds = utilWhiteList.filterRecentGames(
          playedGameList.map(Number),
          window.location.origin
        )
        const headerData = {
          username: this.$store.getters['role/userName']
            ? this.$store.getters['role/userName']
            : null,
          alias: null
        }
        const tmpGameList = await this.$clientApi.game.gameList(
          headerData,
          gameIds,
          this.$i18n.locale
        )
        const gameList = tmpGameList.list
        // 對 gameList 進行排序
        const sortedGameList = gameList.sort((a, b) => {
          const indexA = gameIds.indexOf(a.id)
          const indexB = gameIds.indexOf(b.id)

          // 如果元素在第一個陣列中不存在，則將其放在最後
          if (indexA === -1) return 1
          if (indexB === -1) return -1

          return indexA - indexB
        })

        let setupGames = this.setupRecentGames(sortedGameList)
        let processedGames = await this.fetchRecentGameRtpHandler(
          setupGames.slice(0, this.gameShowMax)
        )
        this.updateGameMaintainList(processedGames)
        processedGames = processedGames.filter((item) => item.maintaining !== true)
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', processedGames)
        return processedGames
      } else {
        return []
      }
    },
    async fetchRecentGameRtpHandler(gameList) {
      let gameIds = []
      // 待後端能提供該款遊戲是否擁有rtp的資訊，需再增加遊戲是否有rtp的判斷
      gameList.forEach((element) => gameIds.push(element.id))
      if (gameIds.length !== 0) {
        // 每 20 筆 ID 發送一個請求
        let promises = []
        for (let i = 0; i < gameIds.length; i += 20) {
          let ids = gameIds.slice(i, i + 20)
          if (ids.length != 0) {
            promises.push(this.$clientApi.game.gameRTPList(ids))
          }
        }
        try {
          // 同時發送所有請求
          let results = await Promise.all(promises)
          // 處理每個請求的結果
          results.forEach((gameRTPList) => {
            if (gameRTPList)
              gameList.forEach((item) => {
                const match = gameRTPList.list.find((obj) => obj.gameId === item.id)
                if (match) {
                  item.dailyRtp = match.dailyRtp
                  item.weeklyRtp = match.weeklyRtp
                  item.monthlyRtp = match.monthlyRtp
                }
              })
          })
        } catch (error) {
          console.log('error', error)
        }
      }
      return gameList
    },
    setupRecentGames(gameList) {
      const gameListValue = gameList
        .filter((game) => game)
        .map((game) => ({
          ...game,
          dailyRtp: undefined,
          weeklyRtp: undefined,
          monthlyRtp: undefined,
          thumbUrl:
            game.thumbPath === null
              ? this.$store.getters['gameHall/gameDefaultImg']
              : `${process.env.IMAGE_URL}${game.thumbPath}`
        }))
      return gameListValue
    },
    async getInCompleteGameData() {
      const userName = this.$store.getters['role/userName']
      const gameIds = await this.$clientApi.game.getInCompleteGameList(userName)
      const headerData = {
        username: userName ? userName : null,
        alias: null
      }
      if (!gameIds.list?.length) {
        return []
      }
      const setupGameIds = gameIds.list.filter(
        (id) => !this.allGameList.some((game) => game.id === id)
      )
      const games = cloneDeep(
        gameIds.list
          .filter((id) => this.allGameList.some((game) => game.id === id))
          .map((id) => this.allGameList.find((game) => game.id === id))
      )
      const { list } =
        setupGameIds.length > 0
          ? await this.$clientApi.game.gameList(headerData, setupGameIds, this.$i18n.locale)
          : { list: [] }

      if (games.length > 0) {
        list.push(...games)
      }
      if (list.length > 0) {
        list.forEach((item) => {
          item.dailyRtp = undefined
          item.weeklyRtp = undefined
          item.monthlyRtp = undefined
          item.thumbUrl =
            item.thumbPath === null
              ? this.$store.getters['gameHall/gameDefaultImg']
              : `${process.env.IMAGE_URL}${item.thumbPath}`
        })
        const sortedGameList = list.sort((a, b) => {
          const indexA = gameIds.list.indexOf(a.id)
          const indexB = gameIds.list.indexOf(b.id)

          // 如果元素在第一個陣列中不存在，則將其放在最後
          if (indexA === -1) return 1
          if (indexB === -1) return -1

          return indexA - indexB
        })
        const processedGames = await this.fetchInCompleteGameRtpHandler(sortedGameList)
        this.updateGameMaintainList(processedGames)
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', processedGames)
        return processedGames
      } else {
        return []
      }
    },
    async fetchInCompleteGameRtpHandler(list) {
      let gameIds = []
      // 待後端能提供該款遊戲是否擁有rtp的資訊，需再增加遊戲是否有rtp的判斷
      list.forEach((element) => gameIds.push(element.id))
      if (gameIds.length !== 0) {
        // 每 20 筆 ID 發送一個請求
        let promises = []
        for (let i = 0; i < gameIds.length; i += 20) {
          let ids = gameIds.slice(i, i + 20)
          if (ids.length != 0) {
            promises.push(this.$clientApi.game.gameRTPList(ids))
          }
        }
        try {
          // 同時發送所有請求
          let results = await Promise.all(promises)
          // 處理每個請求的結果
          results.forEach((gameRTPList) => {
            if (gameRTPList)
              list.forEach((item) => {
                const match = gameRTPList.list.find((obj) => obj.gameId === item.id)
                if (match) {
                  item.dailyRtp = match.dailyRtp
                  item.weeklyRtp = match.weeklyRtp
                  item.monthlyRtp = match.monthlyRtp
                }
              })
          })
        } catch (error) {
          console.log('error', error)
        }
      }
      return list
    },
    async getMaintainList() {
      await this.$store.dispatch('maintain/fetch')
    },
    getGameListId(list) {
      return list.map((obj) => obj.id)
    },
    updateGameMaintainList(gameList) {
      this.platformMaintain.forEach((platformItem) => {
        gameList.forEach((gameItem) => {
          if (platformItem.id === gameItem.platformId && platformItem.maintaining) {
            gameItem.maintaining = true
            gameItem.maintainBeginAt = platformItem.maintainBeginAt
            gameItem.maintainEndAt = platformItem.maintainEndAt
          }
        })
      })
    },
    filterList(ids, list) {
      const filterList = ids
        .map((id) => this.allGameList.find((item) => item.id === id))
        .filter((item) => item !== undefined)
      const finalList = filterList.map((obj) => {
        const match = list.find((a) => a.id === obj.id)
        if (match) {
          return {
            ...obj,
            enable: match.enable,
            name: match.name,
            hasDemo: match.hasDemo,
            hasExp: match.hasExp,
            hasRobot: match.hasRobot,
            thumbUrl: match.thumbUrl
          }
        }
        return obj
      })
      return finalList
    },
    filterFeaturedList(ids, list) {
      const filterList = ids
        .map((id) => this.allGameList.find((item) => item.id === id))
        .filter((item) => item !== undefined)
      const finalList = filterList.map((obj) => {
        const match = list.find((a) => a.id === obj.id)
        if (match) {
          return {
            ...obj,
            bgColor: match.bgColor,
            gameCover: match.gameCover,
            gameImg: match.gameImg,
            title: match.title,
            category: match.category,
            group: match.group
          }
        }
        return obj
      })
      return finalList
    },
    fetchLocalPlayedGameList() {
      let storageJsonData
      // 因為更改陣列結構，改為一維陣列，所以需要判斷是否有舊資料，登入後會刷新舊資料
      storageJsonData = this.$localStorage.get('playedGameList').playedGameList
        ? this.$localStorage.get('playedGameList').playedGameList
        : this.$localStorage.get('playedGameList')
      const storageGameList = storageJsonData ? JSON.parse(storageJsonData) : []
      if (storageGameList.length !== 0) {
        this.$store.commit('gameHall/SET_PLAYED_GAME_LIST', storageGameList)
      }
    },
    async fetchServerPlayedGames() {
      const serverGamesId = await this.$clientApi.game.gamePlayed(this.userName)
      const gameIds = serverGamesId.list.map(Number)
      this.$store.commit('gameHall/SET_PLAYED_GAME_LIST', gameIds)
    }
  },
  watch: {
    playedGameList: {
      async handler(val) {
        this.getRecentGameList(val)
      }
    },
    isLogin: {
      async handler(val) {
        if (val) {
          this.fetchServerPlayedGames()
          // 暫停中遊戲
          // 6/17 通知先關閉該功能
          //this.getInCompleteGameList()
        }
      }
    }
  }
}
