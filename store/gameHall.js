import Vue from 'vue'
const STATION = process.env.STATION
export const state = () => ({
  gameDefaultImg: require(`~/assets/image/${STATION}/game/game_default.webp`),
  // 印度站圖片背景為透明，需使用png才能正常呈現，故無提供jpg圖片
  // 未來統一圖片格式為PNG
  gameDefaultImgJpg:
    STATION === 'india_01'
      ? require(`~/assets/image/${STATION}/game/game_default.png`)
      : require(`~/assets/image/${STATION}/game/game_default.jpg`),
  gameDefaultFeaturedImg: require(`~/assets/image/${STATION}/game/game_img_default.webp`),
  gameDefaultFeaturedImgPng: require(`~/assets/image/${STATION}/game/game_img_default.png`),
  singleGameHallInfo: {},
  gameList: [],
  gameListTotal: 0,
  hotGameList: [],
  gameLink: '',
  xinkey: '',
  openGameLock: false,
  playedGameList: [],
  allGameList: []
})

export const getters = {
  singleGameHallInfo(state) {
    return state.singleGameHallInfo
  },
  gameList(state) {
    return state.gameList
  },
  gameListTotal(state) {
    return state.gameListTotal
  },
  hotGameList(state) {
    return state.hotGameList
  },
  gameDefaultImg(state) {
    return state.gameDefaultImg
  },
  gameLink(state) {
    return state.gameLink
  },
  xinkey(state) {
    return state.xinkey
  },
  openGameLock(state) {
    return state.openGameLock
  },
  gameDefaultImgJpg(state) {
    return state.gameDefaultImgJpg
  },
  gameDefaultFeaturedImg(state) {
    return state.gameDefaultFeaturedImg
  },
  gameDefaultFeaturedImgPng(state) {
    return state.gameDefaultFeaturedImgPng
  },
  playedGameList(state) {
    return state.playedGameList
  },
  allGameList(state) {
    return state.allGameList
  }
}

export const mutations = {
  SET_SINGLEGAMEHALLINFO(state, singleGameHallInfo) {
    state.singleGameHallInfo = singleGameHallInfo
  },
  SET_GAMELIST(state, list) {
    state.gameList = list
  },
  SET_GAMELIST_TOTAL(state, total) {
    state.gameListTotal = total
  },
  SET_ALTIMG(state, { index, ImgPath }) {
    state.gameList[index].thumbUrl = ImgPath
  },
  SET_HOTGAMELIST(state, list) {
    state.hotGameList = list
  },
  SET_GAME_LINK(state, data) {
    state.gameLink = data
  },
  SET_GAMELIST_MAINTAIN_STATUS(state, { gameId, status }) {
    state.gameList.forEach((item) => {
      if (item.id === gameId) {
        item.maintaining = status
      }
    })
  },
  SET_GAMELIST_DISABLED(state, gameId) {
    state.gameList = state.gameList.filter((item) => item.id !== gameId)
  },
  SET_XINKEY(state, data) {
    state.xinkey = data
  },
  SET_OPENGAMELOCK(state, data) {
    state.openGameLock = data
  },
  ADD_PLAYED_GAME_LIST(state, data) {
    state.playedGameList = state.playedGameList.filter((gameId) => data != gameId)
    state.playedGameList.unshift(data)
    if (state.playedGameList.length > 10) state.playedGameList.pop()
    if (process.client)
      this.$localStorage.set('playedGameList', {
        playedGameList: JSON.stringify(state.playedGameList)
      })
  },
  SET_PLAYED_GAME_LIST(state, data) {
    state.playedGameList = data
    if (process.client)
      this.$localStorage.set('playedGameList', {
        playedGameList: JSON.stringify(data)
      })
  },
  ADD_ALL_GAME_LIST(state, data) {
    const list = state.allGameList
    const map = new Map(list.map((item) => [item.id, item]))
    data.forEach((obj) => {
      map.set(obj.id, obj)
    })

    state.allGameList = Array.from(map.values())
  },
  SET_ALL_GAME_LIST(state, data) {
    state.allGameList = data
  },
  SET_ALL_GAME_LIST_MAINTAIN_STATUS(state, { gameId, status }) {
    state.allGameList.forEach((item) => {
      if (item.id === gameId) {
        item.maintaining = status
      }
    })
  },
  SET_ALL_GAME_LIST_DISABLED(state, gameId) {
    state.allGameList = state.allGameList.filter((item) => item.id !== gameId)
  },
  RESET(state) {
    Vue.set(state, 'singleGameHallInfo', {})
    Vue.set(state, 'gameLink', '')
    Vue.set(state, 'xinkey', 'demo')
    Vue.set(state, 'rtpList', [])
    Vue.set(state, 'openGameLock', false)
  }
}
// gameCategoryId 變數統一成 gameCategoryCode
export const actions = {
  async fetchGameList(
    { commit, rootGetters, dispatch },
    { gameCategoryId, sortType, lang, limit, stationName, offset, keyword, providerId }
  ) {
    const headerData = {
      username: rootGetters['role/userName'] ? rootGetters['role/userName'] : null,
      alias: null
    }
    const body = {
      headerData,
      gameCategoryId,
      sortType,
      lang,
      limit,
      stationName,
      offset,
      keyword,
      providerId
    }

    let gameList = await this.$clientApi.game.gameSortList(body)
    commit('SET_GAMELIST_TOTAL', gameList.count)
    gameList = gameList.count > 0 ? gameList.list : []
    // 1. 先處理所有遊戲的縮略圖（共通邏輯）
    if (gameList.length !== 0) {
      // 為所有遊戲設置默認縮略圖
      gameList.forEach((game) => {
        game.thumbUrl =
          game.thumbPath === null
            ? rootGetters['gameHall/gameDefaultImgJpg']
            : `${process.env.IMAGE_URL}${game.thumbPath}`
      })

      // 2. 只為非QC站點處理維護狀態
      const isQcStation =
        process.env.STATION === 'qc_overseas' || process.env.STATION === 'qc_domestic'
      if (!isQcStation) {
        await dispatch('maintain/fetch', null, { root: true })
        const platform = rootGetters['maintain/platform']

        // 建立平台ID到維護信息的映射，避免嵌套循環
        const maintenanceMap = platform.reduce((map, item) => {
          if (item.maintaining) {
            map[item.id] = {
              maintaining: true,
              maintainBeginAt: item.maintainBeginAt,
              maintainEndAt: item.maintainEndAt
            }
          }
          return map
        }, {})

        // 一次遍歷更新遊戲維護狀態
        gameList.forEach((game) => {
          const maintenance = maintenanceMap[game.platformId]
          if (maintenance) {
            Object.assign(game, maintenance)
          }
        })
      }
    }
    if (process.client) {
      // 第一次先show 遊戲列表
      commit('SET_GAMELIST', gameList)

      // ****上線前版面調整後暫時用不到
      // 用於首頁顯示，只需要各遊戲類別show 前 10 筆，
      // if (sortType === 2) {
      //   dispatch('fetchHotGameList', { gameCategoryId })
      // }
    }
    // 返回處理後的 gameList
  },
  // eslint-disable-next-line no-unused-vars
  updateListWhenDisabled({ commit, dispatch }, { gameCategoryId, gameId }) {
    commit('SET_GAMELIST_DISABLED', gameId)
    commit('SET_ALL_GAME_LIST_DISABLED', gameId)
    // ****上線前版面調整後暫時用不到
    // dispatch('fetchHotGameList', { gameCategoryId })
  },
  // ****上線前版面調整後暫時用不到
  // fetchHotGameList({ commit, rootGetters, getters }, { gameCategoryId }) {
  //   const gameCategory = rootGetters['gameProvider/gameCategory']
  //   const gameList = getters.gameList
  //   const hotGameList = []
  //   gameCategory.forEach((gameCategoryItem) => {
  //     gameList.forEach((gameItem) => {
  //       if (gameCategoryId === gameCategoryItem.code) {
  //         if (hotGameList.length < 10) {
  //           hotGameList.push(gameItem)
  //         } else {
  //           return
  //         }
  //       }
  //     })
  //   })
  //   commit('SET_HOTGAMELIST', hotGameList)
  // },
  async fetchGameDemo({ commit }, { headerData, body }) {
    const requestParams = {
      headerData,
      gameId: body.gameId,
      mobile: body.mobile,
      lang: body.lang,
      backUrl: body.backUrl
    }
    if (body.stationName) {
      requestParams.stationName = body.stationName
    }
    let link = await this.$clientApi.game.gameLinkDemo(requestParams)
    let res = ''
    if ('link' in link) {
      res = link.link
    } else {
      res = link.errorCode
    }
    commit('SET_GAME_LINK', res)
  },
  async fetchGameLink({ commit }, { headerData, body }) {
    const requestParams = {
      headerData,
      xinkey: body.xinkey,
      gameId: body.gameId,
      mobile: body.mobile,
      lang: body.lang,
      ip: body.ip,
      backUrl: body.backUrl,
      userAgent: body.userAgent,
      thumbUrl: body.thumbUrl,
      userLevel: body.userLevel ? body.userLevel : 0,
      vipLevel: body.vipLevel ? body.vipLevel : 0
    }
    if (body.stationName) {
      requestParams.stationName = body.stationName
    }
    let link = await this.$clientApi.game.gameLink(requestParams)
    let res = ''
    if ('link' in link) {
      res = link.link
    } else {
      res = link.errorCode
    }
    commit('SET_GAME_LINK', res)
  },
  async fetchRTPList({ commit, getters }, { gameIds }) {
    let gameRTPList = []
    if (gameIds.length !== 0) {
      gameRTPList = await this.$clientApi.game.gameRTPList(gameIds)
      if (
        Object.prototype.hasOwnProperty.call(gameRTPList, 'list') &&
        gameRTPList.list.length !== 0
      ) {
        const mergedArray = getters['gameList'].map((item) => {
          const match = gameRTPList.list.find((obj) => obj.gameId === item.id)
          if (match) {
            // 直接合併到gameList，並清除gameId
            // eslint-disable-next-line no-unused-vars
            const { gameId, ...rest } = match
            return { ...item, ...rest }
          }
          return item
        })
        commit('SET_GAMELIST', mergedArray)
      }
    }
  },
  async fechPlayedGameList({ commit, rootGetters }) {
    const userName = rootGetters['role/userName']
    if (userName !== '' && userName !== undefined && userName !== null) {
      const serverGamesId = await this.$clientApi.game.gamePlayed(userName)
      const gameIds = serverGamesId.list.map(Number)
      commit('SET_PLAYED_GAME_LIST', gameIds)
    } else {
      let storageJsonData
      if (process.client) storageJsonData = this.$localStorage.get('playedGameList').playedGameList
      const storageGameList = storageJsonData ? JSON.parse(storageJsonData) : []
      commit('SET_PLAYED_GAME_LIST', storageGameList)
    }
  }
}
