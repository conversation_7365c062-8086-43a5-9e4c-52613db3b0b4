// 在 store/music.js 文件中

export const state = () => ({
  lobbys: [],
  fishs: [],
  grandPrize: []
})

export const getters = {
  lobbys(state) {
    return state.lobbys
  },
  fishs(state) {
    return state.fishs
  },
  grandPrize(state) {
    return state.grandPrize
  }
}
export const mutations = {
  SET_LOBBY(state, data) {
    state.lobbys = data
  },
  SET_FISH(state, data) {
    state.fishs = data
  },
  SET_GRAND_PRIZE(state, data) {
    state.grandPrize = data
  }
}
export const actions = {
  async fetchGameLobby({ commit }) {
    //取完 json 要將資料存到 local storage內，如果抓取 json 失敗就改抓local storage資料
    try {
      const NUXT_ENV = process.env.NUXT_ENV
      const STATION = process.env.STATION
      const stationConfig = require(`~/station/${STATION}/${NUXT_ENV}`).default
      const pathID = stationConfig.client_id
      let result = await this.$axios.get(
        process.env.IMAGE_URL + `/allstars/${pathID}/game_lobby.json?v=2`
      )
      this.$localStorage.set('gameLobby', result.data)
      commit('SET_LOBBY', result.data)
    } catch (e) {
      commit('SET_LOBBY', this.$localStorage.get('gameLobby'))
    }
  },
  async fetchGameFish({ commit }) {
    //取完 json 要將資料存到 local storage內，如果抓取 json 失敗就改抓local storage資料
    try {
      const NUXT_ENV = process.env.NUXT_ENV
      const STATION = process.env.STATION
      const stationConfig = require(`~/station/${STATION}/${NUXT_ENV}`).default
      const pathID = stationConfig.client_id
      let result = await this.$axios.get(
        process.env.IMAGE_URL + `/allstars/${pathID}/game_fish.json?v=2`
      )
      this.$localStorage.set('gameFish', result.data)
      commit('SET_FISH', result.data)
    } catch (e) {
      commit('SET_FISH', this.$localStorage.get('gameFish'))
    }
  },
  async fetchGrandPrize({ commit, rootGetters }) {
    //取完 json 要將資料存到 local storage內，如果抓取 json 失敗就改抓local storage資料
    try {
      const headerData = {
        username: rootGetters['role/userName'] ? rootGetters['role/userName'] : null,
        alias: null
      }
      const tmpGameList = await this.$clientApi.game.gameList(headerData, [], this.$i18n.locale)
      if (tmpGameList.count === 0) commit('SET_GRAND_PRIZE', this.$localStorage.get('grandPrize'))
      else {
        tmpGameList.list.forEach((game) => {
          game.thumbUrl = `${process.env.IMAGE_URL}${game.thumbPath}`
        })
        this.$localStorage.set('grandPrize', tmpGameList.list)
        commit('SET_GRAND_PRIZE', tmpGameList.list)
      }
    } catch (e) {
      commit('SET_GRAND_PRIZE', this.$localStorage.get('grandPrize'))
    }
  }
}
