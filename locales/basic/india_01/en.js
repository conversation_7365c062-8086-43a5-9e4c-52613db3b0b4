const basic = {
  daily_limit: 'The daily transaction limit is  {coin}.',
  mail_send_noty6: 'Join a Club to unlock the function.',
  mail_send_noty7: 'Can only send mail to the Club Leader.',
  mail_send_noty8: 'Can only send mail to Club members.',
  redeem_gift_noty:
    'Redemption complete. You are now an official member and received 10,000 WinCoins. Happy gaming!',
  redeem_gift_noty1:
    'Redemption complete. Congratulations on receiving 10,000 WinCoins. Happy gaming!',
  vip_level_desc: 'Member Rank Info',
  your_rank: 'Your Rank',
  rank: 'Rank',
  send_mail_description8:
    'Mailing fees for official members of the same Club vary by their member rank.',
  top_up_success_noty2_1: 'You are now a formal member and have been upgraded to Bronze.',
  membership_level_alert: 'Bronze or above is available to participate in the General Chat.',
  opponent_membership_level_not_silver_above:
    'The player is below Silver and is unavailable to receive a private chat.',
  literalNoty: 'Prohibited from entering sensitive words.',
  first_login_noty: 'Login to register, please select your login method.',
  footer_warning5:
    'This website is restricted to adults only. Before playing "JOYOUS PALACE", please confirm that you are over 21 years old.',
  daily_list_desc1: '*Daily ranking resets at: 09:32 PM (GMT****)',
  send_mail_description2: 'Your balance must remain above 20,000 WinCoins.',
  xin_coin: 'WinCoin',
  login: 'Login/Register',
  free_trial_mode_noty: '* WinCoins in demo mode will not be counted.',
  personal_info_active_tooltip:
    'Can be obtained through betting (Every 100K WinCoins bet +30). Daily reduction: -10%',
  device_white_noty1:
    'If you have not received the SMS verification code, please contact your telco service provider or the customer service center of WIN99. Customer service : {mail}',
  device_white_noty3:
    'If you have not received the SMS verification code, please contact your Telco service provider or the WIN99 Support Center, Support Email: {mail}.',
  privacy_policy: 'Policies',
  grand_prize_noty: 'Recent Winnings',
  webPwaInstall: 'Win99 Shortcut Tutorial'
}

export default {
  ...basic
}
