<template>
  <v-container fluid v-show="!maintainSystem[0].maintaining && !status404">
    <!-- title -->
    <v-row align="center" justify="center">
      <div class="pt-2 pt-md-0 pt-lg-0 pt-xl-0 pb-6">
        <linearGradientTitle :title="linearGradientTitleText" />
      </div>
    </v-row>
    <!-- game list card -->

    <v-row justify="end" class="pt-6 align-top px-2" :class="[$UIConfig.gamePage.background]">
      <!-- filter -->
      <v-col
        cols="12"
        :sm="$vuetify.breakpoint.width <= 849 && $vuetify.breakpoint.width >= 600 ? 6 : 4"
        md="3"
        lg="3"
        xl="2"
        class="py-0"
      >
        <v-select
          v-model="gameSortType"
          :label="$t('sort_label')"
          rounded
          outlined
          dense
          attach
          height="40px"
          :items="gameHallSortList"
          item-value="sortType"
          item-text="name"
          :hide-details="$vuetify.breakpoint.xsOnly"
          @change="selectedGameSortEvent"
        />
      </v-col>
      <!-- search -->
      <v-col
        cols="12"
        :sm="$vuetify.breakpoint.width <= 849 && $vuetify.breakpoint.width >= 600 ? 6 : 4"
        md="3"
        lg="3"
        xl="2"
        class="py-0 mt-xl-0 mt-lg-0 mt-md-0 mt-sm-0 mt-4"
      >
        <v-text-field
          v-model="searchWord"
          ref="searchWord"
          append-icon="mdi-magnify"
          clearable
          outlined
          rounded
          dense
          :label="$t('search_games')"
          class="input-height"
          @click:clear="clearSearchEvent"
          @click:append="searchWordEvent"
          @keydown.enter="searchWordEvent"
        />
      </v-col>
      <!-- game list -->
    </v-row>

    <v-row
      v-if="gameHallList.length > 0"
      class="text-left align-top justify-start px-2"
      :class="[$UIConfig.gamePage.background]"
    >
      <v-col
        v-for="(game, index) in gameHallList"
        v-show="showEnableStatus(game.enable)"
        :key="`${index}`"
        cols="6"
        sm="4"
        md="3"
        lg="2"
        class="pa-0"
      >
        <gameCard :game="game" />
      </v-col>
    </v-row>
    <!-- no_results_found notice -->

    <v-row
      v-else
      align="center"
      justify="center"
      :class="[$UIConfig.gamePage.background, 'min-height-120px']"
    >
      <span
        class="custom-text-noto text-caption grey-3--text py-4"
        style="font-size: 12px !important"
        >{{ $t('no_results_found') }}
      </span>
    </v-row>
    <!-- pagination -->

    <v-row v-if="pageTotal" :class="[$UIConfig.gamePage.background]">
      <v-col class="text-center">
        <v-pagination
          v-model="page"
          :length="pageTotal"
          total-visible="7"
          circle
          :color="$UIConfig.defaultBtnColor"
          @input="changePageEvent"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  /**
   * 遊戲頁面更新機制流程：
   *
   * 1. 初始化階段：
   *    - created hook 中調用 initializeWithDefaults() 初始化頁面
   *    - 檢查 URL 參數是否存在所有必要參數 (gameCategory, gameSortType, searchWord, page)
   *    - 如參數不完整則添加預設值並更新 URL
   *    - 如參數已完整則直接使用並獲取遊戲列表
   *
   * 2. 數據監聽階段：
   *    - 監聽路由查詢參數變化 ($route.query)，觸發重新初始化
   *    - 監聽遊戲列表變化 (showGameList)，更新頁面數據並獲取 RTP 資訊
   *
   * 3. 用戶交互處理：
   *    - 排序變更：selectedGameSortEvent() 更新排序類型並重置頁碼
   *    - 關鍵字搜索：searchWordEvent() 更新搜索詞並重置頁碼
   *    - 清除搜索：clearSearchEvent() 清空搜索詞並重置頁碼
   *    - 分頁切換：changePageEvent() 只更新頁碼參數
   *
   * 4. URL 更新機制：
   *    - 所有交互都通過 updatePageQuery() 更新 URL 查詢參數
   *    - 保留現有查詢參數，僅更新需要變更的參數
   *    - 觸發路由變化，進而觸發數據重新獲取
   *
   * 5. 數據獲取流程：
   *    - fetchGameListHandler() 根據最新參數獲取遊戲列表
   *    - fetchGameRtpHandler() 獲取每個遊戲的 RTP 資訊並合併到遊戲數據中
   */
  import gameList from '@/mixins/gameList'
  import analytics from '@/mixins/analytics'
  import scssLoader from '@/mixins/scssLoader.js'
  import _ from 'lodash'
  const STATION = process.env.STATION
  export default {
    mixins: [gameList, analytics, scssLoader],
    name: 'GameListIndex',
    components: {
      linearGradientTitle: () => import(`~/components_station/${STATION}/linearGradientTitle`),
      gameCard: () => import('~/components_station/vietnam_01/game/gameCard.vue')
    },
    data() {
      const gameHallSortList = [
        {
          sortType: 1,
          name: this.$t('all_game')
        },
        {
          sortType: 2,
          name: this.$t('hot_games')
        },
        {
          sortType: 3,
          name: this.$t(this.$UIConfig.gamePage.sortGameName)
        }
      ]

      return {
        gameHallSortList,
        gameHallList: [],
        gameDefault: require(`~/assets/image/${STATION}/game/game_default.webp`),
        showItemLimit: 36,
        page: 1,
        gameSortType: 1,
        searchWord: '',
        gameCategory: 200
      }
    },
    computed: {
      maintainSystem() {
        return this.$store.getters['maintain/system']
      },
      status404() {
        return this.$store.getters['maintain/status404']
      },
      linearGradientTitleText() {
        const category = this.currentCategory(this.gameCategory)
        if (category) {
          return this.$t(category.dict + '_game').toUpperCase()
        }
        return ''
      },
      gameListTotal({ $store }) {
        return $store.getters['gameHall/gameListTotal']
      },
      gameCategoryList({ $store }) {
        return $store.getters['gameProvider/gameCategory']
      },
      showGameList({ $store }) {
        return $store.getters['gameHall/gameList']
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      pageTotal() {
        return Math.ceil(this.gameListTotal / this.showItemLimit)
      },
      offset() {
        return (this.page - 1) * this.showItemLimit
      }
    },
    watch: {
      showGameList: {
        async handler(val) {
          this.$nuxt.$loading.start()
          const gameList = val.map((game) => {
            game.categoryType = this.gameCategory
            return game
          })
          this.gameHallList = gameList
          this.gameHallList = await this.fetchGameRtpHandler(gameList)
          this.$nuxt.$loading.finish()
        },
        deep: true
      },
      '$route.query': {
        handler(newQuery) {
          this.initializeWithDefaults(newQuery)
        },
        deep: true
      }
    },
    created() {
      if (process.client) {
        const currentQuery = this.$route.query
        this.initializeWithDefaults(currentQuery)
      }
    },
    beforeDestroy() {
      this.$store.commit('gameHall/SET_GAMELIST', [])
    },
    methods: {
      // 初始化頁面參數
      async initializeWithDefaults(currentQuery) {
        const defaultValues = {
          page: 1,
          gameCategory: 100,
          gameSortType: 3,
          searchWord: ''
        }
        let needsUpdate = false
        const updatedQuery = { ...currentQuery }
        // 檢查每個預設值是否存在於當前的 query 中，如果不存在則添加
        Object.keys(defaultValues).forEach((key) => {
          // 檢查參數是否存在
          const paramExists =
            // 檢查searchWord參數使否存在並允許null
            (key === 'searchWord' && currentQuery[key] !== undefined) ||
            // 檢查其他參數使否存且不為null
            (currentQuery[key] !== undefined && currentQuery[key] !== null)
          if (!paramExists) {
            updatedQuery[key] = defaultValues[key]
            needsUpdate = true
          }
        })

        if (needsUpdate) {
          this.$router.push({ query: updatedQuery })
        } else {
          await this.updateComponentData(currentQuery)
          this.fetchGameListHandler()
        }
      },
      // 更新頁面參數
      async updateComponentData(query) {
        this.gameCategory = Number(query.gameCategory) || 200
        this.gameSortType = Number(query.gameSortType) || 1
        this.searchWord = query.searchWord || ''
        this.page = Number(query.page) || 1
      },
      // 更新分頁參數
      updatePageQuery(params) {
        const currentQuery = this.$route.query
        const updatedQuery = { ...currentQuery, ...params }
        const isEqual = _.isEqual(currentQuery, updatedQuery)
        // 如果查詢參數有變更
        if (!isEqual) {
          // 更新路由查詢參數,這會觸發路由變化
          this.$router.push({ query: updatedQuery })
        } else {
          // 如果查詢參數沒有變更,直接使用當前參數重新初始化
          this.initializeWithDefaults(updatedQuery)
        }
      },
      // 更新遊戲列表
      async fetchGameListHandler() {
        if (this.gameCategoryList.length > 0) {
          await this.fetchGameList(
            this.gameCategory,
            this.gameSortType,
            this.offset,
            this.showItemLimit,
            this.searchWord
          )
          // 如果搜尋關鍵字不為空，則發送搜尋關鍵字到 analytics
          if (!this.stringNullOrEmpty(this.searchWord)) {
            this.sendGameSearchAnalytics(this.gameHallList, this.searchWord)
          }
        }
      },
      // 更新遊戲列表
      async fetchGameList(gameCategoryId, sortType, offset, limit, keyword) {
        const lang = this.$i18n.locale
        let params = {
          gameCategoryId,
          sortType,
          lang
        }
        if (offset) {
          params.offset = offset
        }
        if (limit) {
          params.limit = limit
        }
        if (keyword && keyword !== '') {
          params.keyword = keyword
        }

        await this.$store.dispatch('gameHall/fetchGameList', params)
      },
      // 取得RTP
      async fetchGameRtpHandler(gameSourceList) {
        // 檢查輸入參數
        if (!gameSourceList?.length) {
          return gameSourceList
        }

        // 使用 map 直接提取 gameIds
        const gameIds = gameSourceList.map((game) => game.id)

        try {
          // 獲取 RTP 數據
          const { list: rtpList = [] } = (await this.$clientApi.game.gameRTPList(gameIds)) || {}

          // 如果沒有 RTP 數據，直接返回原列表
          if (!rtpList?.length) {
            return gameSourceList
          }

          // 使用 Map 結構來提高查找效率 - O(1) vs O(n)
          const rtpMap = new Map(rtpList.map((rtpItem) => [rtpItem.gameId, rtpItem]))
          // 合併 RTP 數據到遊戲列表
          return gameSourceList.map((game) => {
            const rtpData = rtpMap.get(game.id)
            if (rtpData) {
              // eslint-disable-next-line no-unused-vars
              const { gameId, ...rtpInfo } = rtpData
              return { ...game, ...rtpInfo }
            }
            return game
          })
        } catch (error) {
          console.error('RTP 數據獲取失敗:', error)
          // 錯誤情況下返回原始列表
          return gameSourceList
        }
      },

      // 選擇排序事件
      async selectedGameSortEvent() {
        this.page = 1
        this.updatePageQuery({
          page: String(this.page),
          gameSortType: String(this.gameSortType)
        })
      },
      // 關鍵字搜尋事件
      async searchWordEvent() {
        this.page = 1
        this.updatePageQuery({
          page: String(this.page),
          searchWord: this.searchWord
        })
        // 加入此行，解決手機不會收起鍵盤的問題
        this.$refs.searchWord.blur()
      },
      // 清除關鍵字事件
      async clearSearchEvent() {
        this.page = 1
        this.searchWord = ''
        this.updatePageQuery({
          page: this.page,
          searchWord: ''
        })
      },
      // 切換分頁事件
      changePageEvent() {
        this.updatePageQuery({ page: String(this.page) })
        //在 Safari 瀏覽器中，window.scrollTo()方法的behavior參數不支援'smooth'值，只支援'auto'和'instant'兩個值。
        window.scrollTo({ top: 0, behavior: 'auto' })
      },
      // 發送遊戲搜尋分析
      sendGameSearchAnalytics(gameList, searchWord) {
        if (!this.stringNullOrEmpty(searchWord)) {
          this.gameSearchAnalytics({
            username: this.userName,
            content: searchWord,
            gameIds: gameList.map((item) => item.id),
            at: this.$moment().format('YYYY-MM-DDTHH:mm:ssZ')
          })
        }
      },
      currentCategory(categoryId) {
        // 排除無效參數情況
        if (categoryId === undefined || categoryId === null || !this.gameCategoryList?.length) {
          return null
        }
        const numCategoryId = Number(categoryId)
        return this.gameCategoryList.find((item) => Number(item.code) === numCategoryId) || null
      },
      // 檢查字串是否為空
      stringNullOrEmpty(word) {
        return word === null || word === undefined || word === ''
      }
    }
  }
</script>

<style lang="scss" scoped>
  .min-height-120px {
    min-height: 120px !important;
  }

  // 這style目前只有華義用到
  $card-fill-color: map-get($colors, card-fill);
  .bg-color {
    background-color: rgba($card-fill-color, 0.4);
  }

  // 修復 v-select 選項文字被切掉的問題
  ::v-deep .v-select__selections {
    line-height: 1.5 !important;
    min-height: 24px !important;
  }

  ::v-deep .v-list-item__title {
    line-height: 1.5 !important;
    padding: 4px 0 !important;
  }

  ::v-deep .v-list-item {
    min-height: 36px !important;
    padding: 4px 16px !important;
  }

  ::v-deep .v-list-item__content {
    padding: 4px 0 !important;
  }
</style>
